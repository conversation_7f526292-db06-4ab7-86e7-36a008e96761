{"name": "reflex", "scripts": {"dev": "next dev ", "export": "next build ", "export-sitemap": "next build  && next-sitemap", "prod": "next start"}, "dependencies": {"@emotion/react": "11.14.0", "axios": "1.8.3", "json5": "2.2.3", "next": "15.3.0", "next-sitemap": "4.2.3", "next-themes": "0.4.6", "react": "19.0.0", "react-dom": "19.0.0", "react-focus-lock": "2.13.6", "socket.io-client": "4.8.1", "universal-cookie": "7.2.2"}, "devDependencies": {"autoprefixer": "10.4.21", "postcss": "8.5.3", "postcss-import": "16.1.0"}, "overrides": {"react-is": "19.0.0"}}